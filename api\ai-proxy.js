/**
 * SECURE AI API PROXY SERVICE
 * 
 * This service provides secure server-side proxy for all AI API calls,
 * ensuring API keys are never exposed to client-side code.
 * 
 * SECURITY FEATURES:
 * - Server-side API key management
 * - Request authentication and authorization
 * - Rate limiting with emergency bypass
 * - Input validation and sanitization
 * - Comprehensive audit logging
 * - HIPAA-compliant request handling
 */

const express = require('express');
const multer = require('multer');
const rateLimit = require('express-rate-limit');
const { createClient } = require('@supabase/supabase-js');
const FormData = require('form-data');
const fetch = require('node-fetch');
const crypto = require('crypto');

// Initialize Supabase client for authentication
const supabase = createClient(
  process.env.SUPABASE_URL,
  process.env.SUPABASE_SERVICE_ROLE_KEY
);

// AI Service Configuration
const AI_SERVICES = {
  openai: {
    baseUrl: 'https://api.openai.com/v1',
    apiKey: process.env.OPENAI_API_KEY,
    models: {
      whisper: 'whisper-1',
      tts: 'tts-1',
      chat: 'gpt-3.5-turbo'
    }
  },
  elevenlabs: {
    baseUrl: 'https://api.elevenlabs.io/v1',
    apiKey: process.env.ELEVENLABS_API_KEY
  }
};

// Validate required environment variables
const requiredEnvVars = [
  'SUPABASE_URL',
  'SUPABASE_SERVICE_ROLE_KEY',
  'OPENAI_API_KEY',
  'ELEVENLABS_API_KEY'
];

for (const envVar of requiredEnvVars) {
  if (!process.env[envVar]) {
    throw new Error(`Missing required environment variable: ${envVar}`);
  }
}

const router = express.Router();

// Configure multer for audio file uploads
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 25 * 1024 * 1024, // 25MB limit for audio files
    files: 1
  },
  fileFilter: (req, file, cb) => {
    // Validate audio file types
    const allowedMimeTypes = [
      'audio/webm',
      'audio/wav',
      'audio/mp3',
      'audio/m4a',
      'audio/ogg'
    ];
    
    if (allowedMimeTypes.includes(file.mimetype)) {
      cb(null, true);
    } else {
      cb(new Error(`Invalid file type: ${file.mimetype}. Allowed types: ${allowedMimeTypes.join(', ')}`));
    }
  }
});

// Rate limiting configuration with emergency bypass
const createRateLimit = (windowMs, max, emergencyBypass = false) => {
  return rateLimit({
    windowMs,
    max,
    message: {
      success: false,
      error: 'Too many requests. Please try again later.',
      retryAfter: Math.ceil(windowMs / 1000)
    },
    standardHeaders: true,
    legacyHeaders: false,
    skip: (req) => {
      // Emergency bypass for critical medical situations
      if (emergencyBypass && req.headers['x-emergency-override'] === 'true') {
        const userRole = req.user?.user_metadata?.role;
        const emergencyRoles = ['doctor', 'nurse', 'emergency_responder', 'admin'];
        return emergencyRoles.includes(userRole);
      }
      return false;
    },
    onLimitReached: async (req) => {
      // Log rate limit violations
      await logSecurityEvent('rate_limit_exceeded', 'medium', {
        user_id: req.user?.id,
        endpoint: req.path,
        ip_address: req.ip,
        user_agent: req.headers['user-agent']
      });
    }
  });
};

// Authentication middleware
const authenticateUser = async (req, res, next) => {
  try {
    const authHeader = req.headers.authorization;
    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return res.status(401).json({
        success: false,
        error: 'Authentication required',
        code: 'AUTH_REQUIRED'
      });
    }

    const token = authHeader.substring(7);
    const { data: { user }, error } = await supabase.auth.getUser(token);

    if (error || !user) {
      await logSecurityEvent('authentication_failed', 'medium', {
        error: error?.message,
        ip_address: req.ip,
        user_agent: req.headers['user-agent']
      });
      
      return res.status(401).json({
        success: false,
        error: 'Invalid authentication token',
        code: 'AUTH_INVALID'
      });
    }

    req.user = user;
    next();
  } catch (error) {
    console.error('Authentication error:', error);
    res.status(500).json({
      success: false,
      error: 'Authentication failed',
      code: 'AUTH_ERROR'
    });
  }
};

// Request validation middleware
const validateRequest = (requiredFields = []) => {
  return (req, res, next) => {
    const errors = [];
    
    // Check required fields
    for (const field of requiredFields) {
      if (!req.body[field] && !req.file) {
        errors.push(`Missing required field: ${field}`);
      }
    }
    
    // Validate session ID format
    if (req.body.sessionId && !/^[a-f0-9-]{36}$/.test(req.body.sessionId)) {
      errors.push('Invalid session ID format');
    }
    
    if (errors.length > 0) {
      return res.status(400).json({
        success: false,
        error: 'Validation failed',
        details: errors,
        code: 'VALIDATION_ERROR'
      });
    }
    
    next();
  };
};

// Security logging utility
const logSecurityEvent = async (eventType, severity, details) => {
  try {
    await supabase.from('audit_logs').insert({
      event_type: eventType,
      severity,
      details: {
        ...details,
        timestamp: new Date().toISOString(),
        service: 'ai_proxy'
      }
    });
  } catch (error) {
    console.error('Failed to log security event:', error);
  }
};

// Audit logging for AI API calls
const logAIAPICall = async (userId, endpoint, requestData, responseData, duration) => {
  try {
    await supabase.from('audit_logs').insert({
      event_type: 'ai_api_call',
      user_id: userId,
      resource_type: 'ai_service',
      resource_id: endpoint,
      action: 'api_call',
      details: {
        endpoint,
        request_size: JSON.stringify(requestData).length,
        response_size: JSON.stringify(responseData).length,
        duration_ms: duration,
        timestamp: new Date().toISOString(),
        success: !!responseData.success
      }
    });
  } catch (error) {
    console.error('Failed to log AI API call:', error);
  }
};

// =============================================================================
// API ENDPOINTS
// =============================================================================

/**
 * POST /api/speech-to-text
 * Secure proxy for OpenAI Whisper API
 */
router.post('/speech-to-text',
  authenticateUser,
  createRateLimit(60 * 1000, 30, true), // 30 requests per minute with emergency bypass
  upload.single('audio'),
  validateRequest(['sessionId']),
  async (req, res) => {
    const startTime = Date.now();

    try {
      if (!req.file) {
        return res.status(400).json({
          success: false,
          error: 'Audio file is required',
          code: 'MISSING_AUDIO_FILE'
        });
      }

      const { sessionId, language = 'en', temperature = 0.2 } = req.body;

      // Validate session access
      const { data: session, error: sessionError } = await supabase
        .from('consultation_sessions')
        .select('id, patient_id')
        .eq('id', sessionId)
        .single();

      if (sessionError || !session) {
        return res.status(403).json({
          success: false,
          error: 'Invalid or unauthorized session',
          code: 'SESSION_UNAUTHORIZED'
        });
      }

      // Verify user has access to this session
      const hasAccess = session.patient_id === req.user.id ||
                       ['doctor', 'nurse', 'admin'].includes(req.user.user_metadata?.role);

      if (!hasAccess) {
        await logSecurityEvent('unauthorized_session_access', 'high', {
          user_id: req.user.id,
          session_id: sessionId,
          attempted_action: 'speech_to_text'
        });

        return res.status(403).json({
          success: false,
          error: 'Access denied to session',
          code: 'ACCESS_DENIED'
        });
      }

      // Prepare request to OpenAI Whisper
      const formData = new FormData();
      formData.append('file', req.file.buffer, {
        filename: 'audio.webm',
        contentType: req.file.mimetype
      });
      formData.append('model', AI_SERVICES.openai.models.whisper);
      formData.append('language', language);
      formData.append('response_format', 'json');
      formData.append('temperature', temperature.toString());

      const response = await fetch(`${AI_SERVICES.openai.baseUrl}/audio/transcriptions`, {
        method: 'POST',
        headers: {
          'Authorization': `Bearer ${AI_SERVICES.openai.apiKey}`,
          ...formData.getHeaders()
        },
        body: formData
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('OpenAI Whisper API error:', response.status, errorText);

        return res.status(response.status).json({
          success: false,
          error: 'Speech-to-text service unavailable',
          code: 'STT_SERVICE_ERROR'
        });
      }

      const result = await response.json();
      const duration = Date.now() - startTime;

      // Log successful API call
      await logAIAPICall(req.user.id, 'speech-to-text', {
        session_id: sessionId,
        audio_size: req.file.size,
        language
      }, {
        success: true,
        text_length: result.text?.length || 0
      }, duration);

      res.json({
        success: true,
        data: {
          text: result.text,
          confidence: 0.95, // Whisper doesn't provide confidence scores
          duration: req.file.size / 16000, // Rough estimate
          language: language,
          processing_time: duration
        }
      });

    } catch (error) {
      console.error('Speech-to-text proxy error:', error);
      const duration = Date.now() - startTime;

      await logAIAPICall(req.user.id, 'speech-to-text', {
        session_id: req.body.sessionId,
        error: error.message
      }, {
        success: false,
        error: error.message
      }, duration);

      res.status(500).json({
        success: false,
        error: 'Speech-to-text processing failed',
        code: 'STT_PROCESSING_ERROR'
      });
    }
  }
);

/**
 * POST /api/text-to-speech
 * Secure proxy for ElevenLabs TTS API
 */
router.post('/text-to-speech',
  authenticateUser,
  createRateLimit(60 * 1000, 20, true), // 20 requests per minute with emergency bypass
  validateRequest(['text', 'sessionId']),
  async (req, res) => {
    const startTime = Date.now();

    try {
      const { text, sessionId, voiceId = 'pNInz6obpgDQGcFmaJgB', stability = 0.5, similarity_boost = 0.75 } = req.body;

      // Validate text length
      if (text.length > 5000) {
        return res.status(400).json({
          success: false,
          error: 'Text too long. Maximum 5000 characters allowed.',
          code: 'TEXT_TOO_LONG'
        });
      }

      // Validate session access (same as speech-to-text)
      const { data: session, error: sessionError } = await supabase
        .from('consultation_sessions')
        .select('id, patient_id')
        .eq('id', sessionId)
        .single();

      if (sessionError || !session) {
        return res.status(403).json({
          success: false,
          error: 'Invalid or unauthorized session',
          code: 'SESSION_UNAUTHORIZED'
        });
      }

      const hasAccess = session.patient_id === req.user.id ||
                       ['doctor', 'nurse', 'admin'].includes(req.user.user_metadata?.role);

      if (!hasAccess) {
        await logSecurityEvent('unauthorized_session_access', 'high', {
          user_id: req.user.id,
          session_id: sessionId,
          attempted_action: 'text_to_speech'
        });

        return res.status(403).json({
          success: false,
          error: 'Access denied to session',
          code: 'ACCESS_DENIED'
        });
      }

      // Prepare request to ElevenLabs
      const requestBody = {
        text,
        model_id: 'eleven_monolingual_v1',
        voice_settings: {
          stability,
          similarity_boost
        }
      };

      const response = await fetch(`${AI_SERVICES.elevenlabs.baseUrl}/text-to-speech/${voiceId}`, {
        method: 'POST',
        headers: {
          'Accept': 'audio/mpeg',
          'Content-Type': 'application/json',
          'xi-api-key': AI_SERVICES.elevenlabs.apiKey
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('ElevenLabs TTS API error:', response.status, errorText);

        return res.status(response.status).json({
          success: false,
          error: 'Text-to-speech service unavailable',
          code: 'TTS_SERVICE_ERROR'
        });
      }

      const audioBuffer = await response.buffer();
      const duration = Date.now() - startTime;

      // Log successful API call
      await logAIAPICall(req.user.id, 'text-to-speech', {
        session_id: sessionId,
        text_length: text.length,
        voice_id: voiceId
      }, {
        success: true,
        audio_size: audioBuffer.length
      }, duration);

      // Return audio as base64 for client-side processing
      res.json({
        success: true,
        data: {
          audioData: audioBuffer.toString('base64'),
          audioFormat: 'audio/mpeg',
          duration: estimateDuration(text),
          voiceId,
          processing_time: duration
        }
      });

    } catch (error) {
      console.error('Text-to-speech proxy error:', error);
      const duration = Date.now() - startTime;

      await logAIAPICall(req.user.id, 'text-to-speech', {
        session_id: req.body.sessionId,
        error: error.message
      }, {
        success: false,
        error: error.message
      }, duration);

      res.status(500).json({
        success: false,
        error: 'Text-to-speech processing failed',
        code: 'TTS_PROCESSING_ERROR'
      });
    }
  }
);

/**
 * POST /api/ai-chat
 * Secure proxy for OpenAI Chat Completions API
 */
router.post('/ai-chat',
  authenticateUser,
  createRateLimit(60 * 1000, 50, true), // 50 requests per minute with emergency bypass
  validateRequest(['messages', 'sessionId']),
  async (req, res) => {
    const startTime = Date.now();

    try {
      const {
        messages,
        sessionId,
        agentType = 'general-practitioner',
        maxTokens = 500,
        temperature = 0.7,
        stream = false
      } = req.body;

      // Validate messages format
      if (!Array.isArray(messages) || messages.length === 0) {
        return res.status(400).json({
          success: false,
          error: 'Messages must be a non-empty array',
          code: 'INVALID_MESSAGES'
        });
      }

      // Validate session access
      const { data: session, error: sessionError } = await supabase
        .from('consultation_sessions')
        .select('id, patient_id')
        .eq('id', sessionId)
        .single();

      if (sessionError || !session) {
        return res.status(403).json({
          success: false,
          error: 'Invalid or unauthorized session',
          code: 'SESSION_UNAUTHORIZED'
        });
      }

      const hasAccess = session.patient_id === req.user.id ||
                       ['doctor', 'nurse', 'admin'].includes(req.user.user_metadata?.role);

      if (!hasAccess) {
        await logSecurityEvent('unauthorized_session_access', 'high', {
          user_id: req.user.id,
          session_id: sessionId,
          attempted_action: 'ai_chat'
        });

        return res.status(403).json({
          success: false,
          error: 'Access denied to session',
          code: 'ACCESS_DENIED'
        });
      }

      // Add medical context based on agent type
      const systemPrompts = {
        'general-practitioner': 'You are a helpful medical AI assistant providing general health guidance. Always recommend consulting with healthcare professionals for serious concerns.',
        'cardiologist': 'You are a cardiology AI assistant. Provide heart-related health information while emphasizing the importance of professional medical consultation.',
        'nutritionist': 'You are a nutrition AI assistant. Provide dietary and nutrition guidance while recommending professional consultation for specific medical conditions.',
        'psychiatrist': 'You are a mental health AI assistant. Provide supportive guidance while emphasizing the importance of professional mental health care.',
        'emergency': 'You are an emergency medical AI assistant. Provide immediate guidance while strongly recommending emergency medical services when appropriate.'
      };

      const systemMessage = {
        role: 'system',
        content: systemPrompts[agentType] || systemPrompts['general-practitioner']
      };

      const requestBody = {
        model: AI_SERVICES.openai.models.chat,
        messages: [systemMessage, ...messages],
        max_tokens: Math.min(maxTokens, 1000), // Cap at 1000 tokens
        temperature: Math.max(0, Math.min(1, temperature)), // Clamp between 0-1
        stream: false, // Disable streaming for security
        user: req.user.id // For OpenAI usage tracking
      };

      const response = await fetch(`${AI_SERVICES.openai.baseUrl}/chat/completions`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${AI_SERVICES.openai.apiKey}`
        },
        body: JSON.stringify(requestBody)
      });

      if (!response.ok) {
        const errorText = await response.text();
        console.error('OpenAI Chat API error:', response.status, errorText);

        return res.status(response.status).json({
          success: false,
          error: 'AI chat service unavailable',
          code: 'CHAT_SERVICE_ERROR'
        });
      }

      const result = await response.json();
      const duration = Date.now() - startTime;

      // Log successful API call
      await logAIAPICall(req.user.id, 'ai-chat', {
        session_id: sessionId,
        agent_type: agentType,
        message_count: messages.length,
        max_tokens: maxTokens
      }, {
        success: true,
        response_tokens: result.usage?.completion_tokens || 0,
        total_tokens: result.usage?.total_tokens || 0
      }, duration);

      res.json({
        success: true,
        data: {
          content: result.choices[0]?.message?.content || '',
          agentType,
          usage: result.usage,
          processing_time: duration
        }
      });

    } catch (error) {
      console.error('AI chat proxy error:', error);
      const duration = Date.now() - startTime;

      await logAIAPICall(req.user.id, 'ai-chat', {
        session_id: req.body.sessionId,
        error: error.message
      }, {
        success: false,
        error: error.message
      }, duration);

      res.status(500).json({
        success: false,
        error: 'AI chat processing failed',
        code: 'CHAT_PROCESSING_ERROR'
      });
    }
  }
);

// Health check endpoint
router.get('/health', (req, res) => {
  res.json({
    success: true,
    service: 'ai-proxy',
    timestamp: new Date().toISOString(),
    services: {
      openai: !!AI_SERVICES.openai.apiKey,
      elevenlabs: !!AI_SERVICES.elevenlabs.apiKey
    }
  });
});

// Helper function to estimate audio duration
const estimateDuration = (text) => {
  // Rough estimate: ~150 words per minute, ~5 characters per word
  const wordsPerMinute = 150;
  const charactersPerWord = 5;
  const estimatedWords = text.length / charactersPerWord;
  return Math.ceil((estimatedWords / wordsPerMinute) * 60); // Duration in seconds
};

module.exports = { router, authenticateUser, validateRequest, createRateLimit, AI_SERVICES };
