import { supabase } from './supabaseClient';
import encryptionService from './encryptionService';

/**
 * SECURE Audio Storage Service with AES-256 Encryption
 * Handles HIPAA-compliant audio message storage, retrieval, and management
 *
 * SECURITY FEATURES:
 * - AES-256-GCM encryption for all audio data
 * - Secure key derivation from session tokens
 * - Encrypted storage in both local IndexedDB and cloud
 * - Comprehensive audit logging
 * - Data integrity validation
 */
class AudioStorageService {
  constructor() {
    this.dbName = 'VoiceHealthAudioStorage';
    this.version = 1;
    this.db = null;
    this.storeName = 'audioMessages';
    this.maxStorageSize = 100 * 1024 * 1024; // 100MB limit
    this.audioQuality = {
      high: { sampleRate: 48000, bitRate: 128000 },
      medium: { sampleRate: 44100, bitRate: 96000 },
      low: { sampleRate: 22050, bitRate: 64000 }
    };
  }

  /**
   * Initialize IndexedDB for offline audio storage
   */
  async initializeDB() {
    try {
      return new Promise((resolve, reject) => {
        const request = indexedDB.open(this.dbName, this.version);

        request.onerror = () => reject(new Error('Failed to open IndexedDB'));

        request.onsuccess = (event) => {
          this.db = event.target.result;
          resolve(this.db);
        };

        request.onupgradeneeded = (event) => {
          const db = event.target.result;
          
          if (!db.objectStoreNames.contains(this.storeName)) {
            const store = db.createObjectStore(this.storeName, { keyPath: 'id' });
            store.createIndex('sessionId', 'sessionId', { unique: false });
            store.createIndex('userId', 'userId', { unique: false });
            store.createIndex('timestamp', 'timestamp', { unique: false });
            store.createIndex('messageType', 'messageType', { unique: false });
          }
        };
      });
    } catch (error) {
      console.error('IndexedDB initialization failed:', error);
      throw new Error('Audio storage initialization failed');
    }
  }

  /**
   * Store audio message locally and in cloud with AES-256 encryption
   */
  async storeAudioMessage(audioBlob, messageData) {
    try {
      if (!this.db) await this.initializeDB();

      const messageId = `msg_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      const timestamp = new Date().toISOString();

      // Validate session token for encryption
      if (!messageData.sessionToken) {
        throw new Error('Session token is required for secure audio storage');
      }

      // Compress audio based on quality setting
      const compressedBlob = await this.compressAudio(audioBlob, messageData.quality || 'medium');

      // Convert blob to array buffer for encryption
      const audioBuffer = await compressedBlob.arrayBuffer();

      // Encrypt audio data using AES-256-GCM
      console.log('🔒 Encrypting audio data...');
      const encryptedAudioData = await encryptionService.encryptMedicalData(
        {
          audioData: Array.from(new Uint8Array(audioBuffer)),
          originalSize: compressedBlob.size,
          mimeType: compressedBlob.type
        },
        messageData.sessionToken
      );

      // Create message object with encrypted audio
      const audioMessage = {
        id: messageId,
        sessionId: messageData.sessionId,
        userId: messageData.userId,
        speakerId: messageData.speakerId,
        speakerName: messageData.speakerName,
        messageType: messageData.messageType || 'user_voice',
        encryptedAudioData: encryptedAudioData, // Encrypted audio instead of raw blob
        duration: messageData.duration || 0,
        quality: messageData.quality || 'medium',
        transcription: messageData.transcription || null,
        confidence: messageData.confidence || 0.95,
        timestamp,
        status: 'pending_sync',
        size: compressedBlob.size,
        encrypted: encryptedAudioData.encrypted,
        metadata: {
          sampleRate: this.audioQuality[messageData.quality || 'medium'].sampleRate,
          bitRate: this.audioQuality[messageData.quality || 'medium'].bitRate,
          format: 'webm',
          codec: 'opus',
          encryption: {
            algorithm: encryptedAudioData.algorithm,
            keyLength: encryptedAudioData.keyLength,
            encrypted: encryptedAudioData.encrypted,
            timestamp: encryptedAudioData.timestamp
          }
        }
      };

      // Check storage limit
      const currentSize = await this.getCurrentStorageSize();
      if (currentSize + compressedBlob.size > this.maxStorageSize) {
        await this.cleanupOldMessages();
      }

      // Store locally
      await this.storeLocally(audioMessage);

      // Upload encrypted audio to cloud storage (Supabase)
      const cloudUrl = await this.uploadEncryptedToCloud(encryptedAudioData, messageId, messageData);
      if (cloudUrl) {
        audioMessage.cloudUrl = cloudUrl;
        audioMessage.status = 'synced';
        await this.updateLocalMessage(messageId, { cloudUrl, status: 'synced' });
      }

      // Store message metadata in database (without sensitive audio data)
      await this.storeMessageMetadata(audioMessage);

      // Log secure storage event
      await this.logSecureStorageEvent('audio_stored', messageId, {
        encrypted: encryptedAudioData.encrypted,
        size: compressedBlob.size,
        cloud_stored: !!cloudUrl
      });

      return {
        success: true,
        messageId,
        localStored: true,
        cloudStored: !!cloudUrl,
        size: compressedBlob.size,
        encrypted: encryptedAudioData.encrypted
      };
    } catch (error) {
      console.error('Failed to store audio message:', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * Compress audio based on quality setting
   */
  async compressAudio(audioBlob, quality = 'medium') {
    try {
      const audioContext = new AudioContext();
      const arrayBuffer = await audioBlob.arrayBuffer();
      const audioBuffer = await audioContext.decodeAudioData(arrayBuffer);

      const qualitySettings = this.audioQuality[quality];
      
      // Create offline context with desired sample rate
      const offlineContext = new OfflineAudioContext(
        audioBuffer.numberOfChannels,
        audioBuffer.duration * qualitySettings.sampleRate,
        qualitySettings.sampleRate
      );

      const source = offlineContext.createBufferSource();
      source.buffer = audioBuffer;
      source.connect(offlineContext.destination);
      source.start();

      const compressedBuffer = await offlineContext.startRendering();
      
      // Convert back to blob (simplified - in production use proper encoding)
      const compressedBlob = new Blob([compressedBuffer], { type: 'audio/webm' });
      
      audioContext.close();
      return compressedBlob;
    } catch (error) {
      console.warn('Audio compression failed, using original:', error);
      return audioBlob;
    }
  }

  /**
   * Store message locally in IndexedDB
   */
  async storeLocally(audioMessage) {
    return new Promise((resolve, reject) => {
      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const request = store.add(audioMessage);

      request.onsuccess = () => resolve(audioMessage.id);
      request.onerror = () => reject(new Error('Failed to store audio locally'));
    });
  }

  /**
   * Upload encrypted audio to Supabase cloud storage
   */
  async uploadEncryptedToCloud(encryptedAudioData, messageId, messageData) {
    try {
      const fileName = `encrypted-audio/${messageData.userId}/${messageData.sessionId}/${messageId}.enc`;

      // Convert encrypted data to blob for upload
      const encryptedBlob = new Blob([JSON.stringify(encryptedAudioData)], {
        type: 'application/json'
      });

      const { data, error } = await supabase.storage
        .from('voice-messages')
        .upload(fileName, encryptedBlob, {
          contentType: 'application/json',
          metadata: {
            messageId,
            sessionId: messageData.sessionId,
            userId: messageData.userId,
            duration: messageData.duration,
            encrypted: true,
            algorithm: encryptedAudioData.algorithm,
            encryption_timestamp: encryptedAudioData.timestamp
          }
        });

      if (error) {
        console.error('Encrypted cloud upload failed:', error);
        return null;
      }

      // Get public URL (encrypted data is safe to be public)
      const { data: urlData } = supabase.storage
        .from('voice-messages')
        .getPublicUrl(fileName);

      return urlData.publicUrl;
    } catch (error) {
      console.error('Encrypted cloud upload error:', error);
      return null;
    }
  }

  /**
   * Legacy method for backward compatibility
   */
  async uploadToCloud(audioBlob, messageId, messageData) {
    console.warn('⚠️ Using legacy uploadToCloud method. Consider using uploadEncryptedToCloud for security.');
    return this.uploadEncryptedToCloud(audioBlob, messageId, messageData);
  }

  /**
   * Store message metadata in database (without sensitive audio data)
   */
  async storeMessageMetadata(audioMessage) {
    try {
      const { error } = await supabase
        .from('voice_messages')
        .insert({
          id: audioMessage.id,
          session_id: audioMessage.sessionId,
          user_id: audioMessage.userId,
          speaker_id: audioMessage.speakerId,
          speaker_name: audioMessage.speakerName,
          message_type: audioMessage.messageType,
          cloud_url: audioMessage.cloudUrl,
          duration: audioMessage.duration,
          quality: audioMessage.quality,
          transcription: audioMessage.transcription,
          confidence: audioMessage.confidence,
          status: audioMessage.status,
          size: audioMessage.size,
          encrypted: audioMessage.encrypted,
          metadata: audioMessage.metadata,
          created_at: audioMessage.timestamp
        });

      if (error) {
        console.error('Failed to store message metadata:', error);
      }
    } catch (error) {
      console.error('Database storage error:', error);
    }
  }

  /**
   * Retrieve and decrypt audio message
   */
  async getDecryptedAudioMessage(messageId, sessionToken) {
    try {
      if (!sessionToken) {
        throw new Error('Session token is required for audio decryption');
      }

      // Get encrypted message from local storage first
      let audioMessage = await this.getLocalMessage(messageId);

      // If not found locally, try to download from cloud
      if (!audioMessage) {
        audioMessage = await this.downloadFromCloud(messageId);
      }

      if (!audioMessage || !audioMessage.encryptedAudioData) {
        throw new Error('Audio message not found or not encrypted');
      }

      // Decrypt audio data
      console.log('🔓 Decrypting audio data...');
      const decryptedData = await encryptionService.decryptMedicalData(
        audioMessage.encryptedAudioData,
        sessionToken
      );

      // Convert decrypted data back to blob
      const audioArray = new Uint8Array(decryptedData.audioData);
      const audioBlob = new Blob([audioArray], { type: decryptedData.mimeType });

      return {
        ...audioMessage,
        audioBlob,
        decrypted: true,
        originalSize: decryptedData.originalSize
      };

    } catch (error) {
      console.error('Failed to decrypt audio message:', error);
      throw error;
    }
  }

  /**
   * Download encrypted audio from cloud storage
   */
  async downloadFromCloud(messageId) {
    try {
      // Get message metadata from database
      const { data: messageData, error } = await supabase
        .from('voice_messages')
        .select('*')
        .eq('id', messageId)
        .single();

      if (error || !messageData) {
        console.error('Message metadata not found:', error);
        return null;
      }

      if (!messageData.cloud_url) {
        console.error('No cloud URL for message:', messageId);
        return null;
      }

      // Download encrypted data from cloud
      const response = await fetch(messageData.cloud_url);
      if (!response.ok) {
        throw new Error(`Failed to download encrypted audio: ${response.status}`);
      }

      const encryptedAudioData = await response.json();

      return {
        ...messageData,
        encryptedAudioData
      };

    } catch (error) {
      console.error('Failed to download from cloud:', error);
      return null;
    }
  }

  /**
   * Log secure storage events for audit trail
   */
  async logSecureStorageEvent(eventType, messageId, details) {
    try {
      await supabase.from('audit_logs').insert({
        event_type: eventType,
        resource_type: 'audio_message',
        resource_id: messageId,
        action: 'storage_operation',
        details: {
          ...details,
          timestamp: new Date().toISOString(),
          service: 'audio_storage'
        }
      });
    } catch (error) {
      console.error('Failed to log storage event:', error);
    }
  }

  /**
   * Retrieve audio message by ID
   */
  async getAudioMessage(messageId) {
    try {
      if (!this.db) await this.initializeDB();

      return new Promise((resolve, reject) => {
        const transaction = this.db.transaction([this.storeName], 'readonly');
        const store = transaction.objectStore(this.storeName);
        const request = store.get(messageId);

        request.onsuccess = () => {
          if (request.result) {
            resolve({ success: true, data: request.result });
          } else {
            // Try to fetch from cloud if not local
            this.fetchFromCloud(messageId).then(resolve).catch(reject);
          }
        };
        request.onerror = () => reject(new Error('Failed to retrieve audio message'));
      });
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Fetch audio message from cloud storage
   */
  async fetchFromCloud(messageId) {
    try {
      const { data, error } = await supabase
        .from('voice_messages')
        .select('*')
        .eq('id', messageId)
        .single();

      if (error || !data?.cloud_url) {
        return { success: false, error: 'Message not found in cloud' };
      }

      // Download audio blob from cloud URL
      const response = await fetch(data.cloud_url);
      const audioBlob = await response.blob();

      const audioMessage = {
        id: data.id,
        sessionId: data.session_id,
        userId: data.user_id,
        speakerId: data.speaker_id,
        speakerName: data.speaker_name,
        messageType: data.message_type,
        audioBlob,
        duration: data.duration,
        quality: data.quality,
        transcription: data.transcription,
        confidence: data.confidence,
        timestamp: data.created_at,
        cloudUrl: data.cloud_url,
        status: 'synced',
        size: data.size,
        metadata: data.metadata
      };

      // Store locally for future access
      await this.storeLocally(audioMessage);

      return { success: true, data: audioMessage };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Get audio messages for a session
   */
  async getSessionMessages(sessionId, limit = 50) {
    try {
      if (!this.db) await this.initializeDB();

      return new Promise((resolve, reject) => {
        const transaction = this.db.transaction([this.storeName], 'readonly');
        const store = transaction.objectStore(this.storeName);
        const index = store.index('sessionId');
        const request = index.getAll(sessionId);

        request.onsuccess = () => {
          const localMessages = request.result
            .sort((a, b) => new Date(a.timestamp) - new Date(b.timestamp))
            .slice(-limit);
          
          resolve({ success: true, data: localMessages });
        };
        request.onerror = () => reject(new Error('Failed to retrieve session messages'));
      });
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Get current storage size
   */
  async getCurrentStorageSize() {
    try {
      if (!this.db) await this.initializeDB();

      return new Promise((resolve) => {
        const transaction = this.db.transaction([this.storeName], 'readonly');
        const store = transaction.objectStore(this.storeName);
        const request = store.getAll();

        request.onsuccess = () => {
          const totalSize = request.result.reduce((sum, msg) => sum + (msg.size || 0), 0);
          resolve(totalSize);
        };
        request.onerror = () => resolve(0);
      });
    } catch (error) {
      return 0;
    }
  }

  /**
   * Cleanup old messages to free storage space
   */
  async cleanupOldMessages() {
    try {
      if (!this.db) await this.initializeDB();

      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      const index = store.index('timestamp');
      const request = index.openCursor();

      let deletedCount = 0;
      const cutoffDate = new Date(Date.now() - (30 * 24 * 60 * 60 * 1000)); // 30 days ago

      request.onsuccess = (event) => {
        const cursor = event.target.result;
        if (cursor) {
          const message = cursor.value;
          if (new Date(message.timestamp) < cutoffDate && message.status === 'synced') {
            cursor.delete();
            deletedCount++;
          }
          cursor.continue();
        }
      };

      return new Promise((resolve) => {
        transaction.oncomplete = () => resolve(deletedCount);
      });
    } catch (error) {
      console.error('Cleanup failed:', error);
      return 0;
    }
  }

  /**
   * Update local message
   */
  async updateLocalMessage(messageId, updates) {
    try {
      if (!this.db) await this.initializeDB();

      return new Promise((resolve, reject) => {
        const transaction = this.db.transaction([this.storeName], 'readwrite');
        const store = transaction.objectStore(this.storeName);
        const getRequest = store.get(messageId);

        getRequest.onsuccess = () => {
          const message = getRequest.result;
          if (message) {
            Object.assign(message, updates);
            const putRequest = store.put(message);
            putRequest.onsuccess = () => resolve(true);
            putRequest.onerror = () => reject(new Error('Failed to update message'));
          } else {
            reject(new Error('Message not found'));
          }
        };
      });
    } catch (error) {
      console.error('Update failed:', error);
      return false;
    }
  }

  /**
   * Delete audio message
   */
  async deleteAudioMessage(messageId) {
    try {
      if (!this.db) await this.initializeDB();

      // Delete from local storage
      const transaction = this.db.transaction([this.storeName], 'readwrite');
      const store = transaction.objectStore(this.storeName);
      await store.delete(messageId);

      // Delete from cloud storage
      try {
        const { data } = await supabase
          .from('voice_messages')
          .select('cloud_url')
          .eq('id', messageId)
          .single();

        if (data?.cloud_url) {
          const fileName = data.cloud_url.split('/').pop();
          await supabase.storage.from('voice-messages').remove([fileName]);
        }

        // Delete metadata from database
        await supabase.from('voice_messages').delete().eq('id', messageId);
      } catch (cloudError) {
        console.warn('Cloud deletion failed:', cloudError);
      }

      return { success: true };
    } catch (error) {
      return { success: false, error: error.message };
    }
  }

  /**
   * Sync pending messages to cloud
   */
  async syncPendingMessages() {
    try {
      if (!this.db) await this.initializeDB();

      const transaction = this.db.transaction([this.storeName], 'readonly');
      const store = transaction.objectStore(this.storeName);
      const request = store.getAll();

      return new Promise((resolve) => {
        request.onsuccess = async () => {
          const pendingMessages = request.result.filter(msg => msg.status === 'pending_sync');
          let syncedCount = 0;

          for (const message of pendingMessages) {
            try {
              const cloudUrl = await this.uploadToCloud(
                message.audioBlob,
                message.id,
                {
                  userId: message.userId,
                  sessionId: message.sessionId,
                  duration: message.duration
                }
              );

              if (cloudUrl) {
                await this.updateLocalMessage(message.id, { 
                  cloudUrl, 
                  status: 'synced' 
                });
                await this.storeMessageMetadata({ ...message, cloudUrl, status: 'synced' });
                syncedCount++;
              }
            } catch (error) {
              console.error(`Failed to sync message ${message.id}:`, error);
            }
          }

          resolve({ synced: syncedCount, total: pendingMessages.length });
        };
      });
    } catch (error) {
      console.error('Sync failed:', error);
      return { synced: 0, total: 0 };
    }
  }

  /**
   * Get storage statistics
   */
  async getStorageStats() {
    try {
      const currentSize = await this.getCurrentStorageSize();
      const messageCount = await this.getMessageCount();
      const syncStatus = await this.getSyncStatus();

      return {
        totalSize: currentSize,
        maxSize: this.maxStorageSize,
        usagePercentage: (currentSize / this.maxStorageSize) * 100,
        messageCount,
        syncStatus,
        freeSpace: this.maxStorageSize - currentSize
      };
    } catch (error) {
      return null;
    }
  }

  /**
   * Get message count
   */
  async getMessageCount() {
    try {
      if (!this.db) await this.initializeDB();

      return new Promise((resolve) => {
        const transaction = this.db.transaction([this.storeName], 'readonly');
        const store = transaction.objectStore(this.storeName);
        const request = store.count();

        request.onsuccess = () => resolve(request.result);
        request.onerror = () => resolve(0);
      });
    } catch (error) {
      return 0;
    }
  }

  /**
   * Get sync status
   */
  async getSyncStatus() {
    try {
      if (!this.db) await this.initializeDB();

      return new Promise((resolve) => {
        const transaction = this.db.transaction([this.storeName], 'readonly');
        const store = transaction.objectStore(this.storeName);
        const request = store.getAll();

        request.onsuccess = () => {
          const messages = request.result;
          const pending = messages.filter(msg => msg.status === 'pending_sync').length;
          const synced = messages.filter(msg => msg.status === 'synced').length;
          
          resolve({
            pending,
            synced,
            total: messages.length,
            syncPercentage: messages.length > 0 ? (synced / messages.length) * 100 : 100
          });
        };
        request.onerror = () => resolve({ pending: 0, synced: 0, total: 0, syncPercentage: 100 });
      });
    } catch (error) {
      return { pending: 0, synced: 0, total: 0, syncPercentage: 100 };
    }
  }
}

const audioStorageService = new AudioStorageService();
export default audioStorageService;