import React, { useState, useEffect, useRef } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import Icon from '../../components/AppIcon';
import Button from '../../components/ui/Button';
import Header from '../../components/ui/Header';
import ConsultationStatusIndicator from '../../components/ui/ConsultationStatusIndicator';
import PaymentModal from '../../components/ui/PaymentModal';
import VoiceActivationButton from './components/VoiceActivationButton';
import RealTimeTranscription from './components/RealTimeTranscription';
import AudioPlaybackControls from './components/AudioPlaybackControls';
import AudioVisualization from './components/AudioVisualization';
import WebSocketManager from './components/WebSocketManager';
import SecurityControls from './components/SecurityControls';
import NoiseSuppressionControls from './components/NoiseSuppressionControls';
import ConversationHistorySidebar from './components/ConversationHistorySidebar';
import MultiAgentStatus from './components/MultiAgentStatus';
import ActiveAgentIndicator from './components/ActiveAgentIndicator';
import SessionProgressIndicator from './components/SessionProgressIndicator';
import QuickActionControls from './components/QuickActionControls';
import AsyncMessagePanel from './components/AsyncMessagePanel';
import { usePayment } from '../../contexts/PaymentContext';
import { useAuth } from '../../contexts/AuthContext';
import asyncMessagingService from '../../utils/asyncMessagingService';
import audioStorageService from '../../utils/audioStorageService';

const EnhancedVoiceConsultationInterface = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const { user } = useAuth();
  const { 
    checkConsultationEligibility, 
    hasActiveSubscription, 
    getRemainingCredits,
    payForConsultation 
  } = usePayment();

  // Core Session States
  const [isSessionActive, setIsSessionActive] = useState(false);
  const [currentPhase, setCurrentPhase] = useState('ready'); // 'ready', 'listening', 'processing', 'responding', 'collaborating'
  const [sessionProgress, setSessionProgress] = useState(0);
  const [isLoading, setIsLoading] = useState(true);
  const [showPaymentModal, setShowPaymentModal] = useState(false);
  const [paymentRequired, setPaymentRequired] = useState(false);
  const [consultationFee] = useState(2000);
  const [eligibilityStatus, setEligibilityStatus] = useState(null);

  // Audio System States
  const [audioContext, setAudioContext] = useState(null);
  const [mediaStream, setMediaStream] = useState(null);
  const [mediaRecorder, setMediaRecorder] = useState(null);
  const [audioAnalyser, setAudioAnalyser] = useState(null);
  const [audioLevel, setAudioLevel] = useState(0);
  const [isRecording, setIsRecording] = useState(false);
  const [isPlayingResponse, setIsPlayingResponse] = useState(false);
  const [audioQuality, setAudioQuality] = useState('high');
  const [bufferingStatus, setBufferingStatus] = useState(0);

  // Web Audio API States
  const [gainNode, setGainNode] = useState(null);
  const [noiseReduction, setNoiseReduction] = useState(true);
  const [echoCancellation, setEchoCancellation] = useState(true);
  const [autoGainControl, setAutoGainControl] = useState(true);
  const [frequencyData, setFrequencyData] = useState(new Uint8Array(256));

  // WebSocket States
  const [websocket, setWebsocket] = useState(null);
  const [connectionStatus, setConnectionStatus] = useState('disconnected');
  const [connectionQuality, setConnectionQuality] = useState('excellent');
  const [reconnectAttempts, setReconnectAttempts] = useState(0);

  // Security States
  const [encryptionEnabled, setEncryptionEnabled] = useState(true);
  const [sessionToken, setSessionToken] = useState(null);
  const [localProcessing, setLocalProcessing] = useState(false);
  const [dataRetentionDays, setDataRetentionDays] = useState(30);
  const [consentGiven, setConsentGiven] = useState(false);

  // Async Messaging States
  const [showAsyncMessages, setShowAsyncMessages] = useState(false);
  const [asyncMessageNotifications, setAsyncMessageNotifications] = useState([]);
  const [storageStats, setStorageStats] = useState(null);

  // Session Data
  const [sessionData, setSessionData] = useState({
    id: null,
    startTime: null,
    duration: 0,
    transcriptMessages: [],
    activeAgents: [],
    conversationHistory: [],
    audioSessions: [],
    speakerDiarization: [],
    asyncMessages: []
  });

  // UI States
  const [showConversationHistory, setShowConversationHistory] = useState(false);
  const [selectedAgent, setSelectedAgent] = useState(null);
  const [showSecurityPanel, setShowSecurityPanel] = useState(false);
  const [showAudioControls, setShowAudioControls] = useState(false);

  // Emergency Stop States
  const [emergencyStopTriggered, setEmergencyStopTriggered] = useState(false);
  const [emergencyStopReason, setEmergencyStopReason] = useState('');
  const [showEmergencyDialog, setShowEmergencyDialog] = useState(false);
  const [emergencyStopTime, setEmergencyStopTime] = useState(null);

  const animationFrameRef = useRef();
  const audioChunksRef = useRef([]);
  const emergencyStopRef = useRef(false);

  // Mock active agents with enhanced data
  const activeAgents = [
    {
      id: 'agent_1',
      name: 'Dr. Sarah Chen',
      specialty: 'General Practitioner',
      avatar: 'https://images.unsplash.com/photo-1559839734-2b71ea197ec2?w=400&h=400&fit=crop&crop=face',
      isActive: true,
      confidence: 95,
      voiceProfile: 'Professional Female, American',
      processingTime: 1.2,
      collaborationWeight: 0.8
    },
    {
      id: 'agent_2',
      name: 'Dr. Michael Rodriguez',
      specialty: 'Cardiologist',
      avatar: 'https://images.unsplash.com/photo-1612349317150-e413f6a5b16d?w=400&h=400&fit=crop&crop=face',
      isActive: false,
      confidence: 92,
      voiceProfile: 'Warm Male, Spanish',
      processingTime: 1.5,
      collaborationWeight: 0.6
    },
    {
      id: 'agent_3',
      name: 'Dr. Emily Watson',
      specialty: 'Mental Health',
      avatar: 'https://images.unsplash.com/photo-1594824970753-5dc1d9d0b66b?w=400&h=400&fit=crop&crop=face',
      isActive: false,
      confidence: 89,
      voiceProfile: 'Calm Female, British',
      processingTime: 1.8,
      collaborationWeight: 0.7
    }
  ];

  // Resume session from navigation state
  const resumeSessionId = location.state?.resumeSessionId;
  const resumeMessageId = new URLSearchParams(location.search).get('message');

  useEffect(() => {
    if (!user) {
      navigate('/authentication-demo-access');
      return;
    }

    initializeConsultation();
    setupAudioContext();
    initializeAsyncMessaging();
    
    return () => {
      cleanup();
    };
  }, [user, navigate]);

  useEffect(() => {
    // Session timer
    let interval;
    if (isSessionActive) {
      interval = setInterval(() => {
        setSessionData(prev => ({
          ...prev,
          duration: prev.duration + 1
        }));
      }, 1000);
    }
    return () => clearInterval(interval);
  }, [isSessionActive]);

  useEffect(() => {
    // Audio level monitoring
    if (isRecording && audioAnalyser) {
      const updateAudioLevel = () => {
        const dataArray = new Uint8Array(audioAnalyser.frequencyBinCount);
        audioAnalyser.getByteFrequencyData(dataArray);
        
        const average = dataArray.reduce((a, b) => a + b) / dataArray.length;
        setAudioLevel(average);
        setFrequencyData(dataArray);
        
        animationFrameRef.current = requestAnimationFrame(updateAudioLevel);
      };
      updateAudioLevel();
    } else {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
      setAudioLevel(0);
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current);
      }
    };
  }, [isRecording, audioAnalyser]);

  useEffect(() => {
    // Handle specific async message if coming from notification
    if (resumeMessageId && user?.id) {
      handleOpenAsyncMessage(resumeMessageId);
    }
  }, [resumeMessageId, user?.id]);

  const initializeAsyncMessaging = async () => {
    try {
      // Initialize audio storage
      await audioStorageService.initializeDB();
      
      // Get storage statistics
      const stats = await audioStorageService.getStorageStats();
      setStorageStats(stats);

      // Request notification permission
      if ('Notification' in window && Notification.permission === 'default') {
        await Notification.requestPermission();
      }

      // Sync pending messages
      if (navigator.onLine) {
        asyncMessagingService.syncPendingMessages();
      }

      // Load async message notifications from localStorage
      const savedNotifications = JSON.parse(localStorage.getItem('voicehealth_notifications') || '[]');
      setAsyncMessageNotifications(savedNotifications);

      // Listen for new async messages
      window.addEventListener('voiceMessageReceived', handleAsyncMessageReceived);

    } catch (error) {
      console.error('Failed to initialize async messaging:', error);
    }
  };

  const handleAsyncMessageReceived = (event) => {
    const notification = event.detail;
    setAsyncMessageNotifications(prev => {
      const exists = prev.find(n => n.messageId === notification.messageId);
      if (!exists) {
        return [notification, ...prev.slice(0, 49)]; // Keep latest 50
      }
      return prev;
    });
    
    // Show browser notification if permission granted
    if ('Notification' in window && Notification.permission === 'granted') {
      new Notification('New Voice Message', {
        body: `${formatDuration(notification.duration)} voice message received`,
        icon: '/favicon.ico',
        tag: `voice-message-${notification.messageId}`
      });
    }
  };

  const handleOpenAsyncMessage = async (messageId) => {
    try {
      const result = await asyncMessagingService.getMessage(messageId, user.id);
      if (result.success) {
        setShowAsyncMessages(true);
        // Mark notification as read
        setAsyncMessageNotifications(prev => 
          prev.filter(n => n.messageId !== messageId)
        );
        localStorage.setItem('voicehealth_notifications', 
          JSON.stringify(asyncMessageNotifications.filter(n => n.messageId !== messageId))
        );
      }
    } catch (error) {
      console.error('Failed to open async message:', error);
    }
  };

  const setupAudioContext = async () => {
    try {
      const context = new (window.AudioContext || window.webkitAudioContext)();
      setAudioContext(context);

      // Setup audio nodes
      const gain = context.createGain();
      gain.gain.value = 1.0;
      setGainNode(gain);
    } catch (error) {
      console.error('Failed to setup audio context:', error);
    }
  };

  const initializeConsultation = async () => {
    try {
      setIsLoading(true);

      // Check if user can start consultation
      const eligibilityResult = await checkConsultationEligibility();
      
      if (!eligibilityResult.success) {
        console.error('Eligibility check failed:', eligibilityResult.error);
        setEligibilityStatus({ canStart: false, reason: 'error' });
        return;
      }

      setEligibilityStatus(eligibilityResult);

      // If user can't start consultation, show payment options
      if (!eligibilityResult.canStart) {
        if (eligibilityResult.reason === 'no_subscription') {
          navigate('/payment-plans');
          return;
        } else if (eligibilityResult.reason === 'no_credits') {
          setPaymentRequired(true);
        }
      }

      // Generate secure session token
      setSessionToken(generateSecureToken());

      // If resuming session, load session data
      if (resumeSessionId) {
        await loadSessionData(resumeSessionId);
      }
    } catch (error) {
      console.error('Consultation initialization error:', error);
      setEligibilityStatus({ canStart: false, reason: 'error' });
    } finally {
      setIsLoading(false);
    }
  };

  const generateSecureToken = () => {
    const array = new Uint8Array(32);
    crypto.getRandomValues(array);
    return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
  };

  const setupWebSocketConnection = () => {
    if (!sessionToken) return;

    try {
      const ws = new WebSocket(`wss://api.healthai.com/consultation/${sessionData.id}?token=${sessionToken}`);
      
      ws.onopen = () => {
        setConnectionStatus('connected');
        setConnectionQuality('excellent');
        setReconnectAttempts(0);
      };

      ws.onmessage = (event) => {
        const data = JSON.parse(event.data);
        handleWebSocketMessage(data);
      };

      ws.onclose = () => {
        setConnectionStatus('disconnected');
        if (isSessionActive) {
          attemptReconnection();
        }
      };

      ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        setConnectionQuality('poor');
      };

      setWebsocket(ws);
    } catch (error) {
      console.error('Failed to setup WebSocket:', error);
    }
  };

  const attemptReconnection = () => {
    if (reconnectAttempts < 5) {
      setTimeout(() => {
        setReconnectAttempts(prev => prev + 1);
        setupWebSocketConnection();
      }, Math.pow(2, reconnectAttempts) * 1000);
    }
  };

  const handleWebSocketMessage = (data) => {
    switch (data.type) {
      case 'transcription': addTranscriptMessage('agent', data.content, data.confidence, data.speaker);
        break;
      case 'agent_collaboration':
        handleAgentCollaboration(data);
        break;
      case 'audio_response':
        handleAudioResponse(data);
        break;
      case 'session_update':
        updateSessionProgress(data.progress);
        break;
      case 'async_message':
        handleAsyncMessageReceived({ detail: data });
        break;
      default:
        console.log('Unknown message type:', data.type);
    }
  };

  const initializeAudioCapture = async () => {
    try {
      const constraints = {
        audio: {
          echoCancellation: echoCancellation,
          noiseSuppression: noiseReduction,
          autoGainControl: autoGainControl,
          sampleRate: audioQuality === 'high' ? 48000 : 44100,
          channelCount: 2
        }
      };

      const stream = await navigator.mediaDevices.getUserMedia(constraints);
      setMediaStream(stream);

      if (audioContext) {
        const source = audioContext.createMediaStreamSource(stream);
        const analyser = audioContext.createAnalyser();
        
        analyser.fftSize = 512;
        analyser.smoothingTimeConstant = 0.3;
        
        source.connect(analyser);
        if (gainNode) {
          analyser.connect(gainNode);
        }
        
        setAudioAnalyser(analyser);
      }

      // Setup MediaRecorder for audio streaming
      const recorder = new MediaRecorder(stream, {
        mimeType: 'audio/webm; codecs=opus',
        audioBitsPerSecond: audioQuality === 'high' ? 128000 : 64000
      });

      recorder.ondataavailable = (event) => {
        if (event.data.size > 0) {
          audioChunksRef.current.push(event.data);
          if (websocket && websocket.readyState === WebSocket.OPEN) {
            sendAudioChunk(event.data);
          }
        }
      };

      setMediaRecorder(recorder);

    } catch (error) {
      console.error('Failed to initialize audio capture:', error);
      alert('Failed to access microphone. Please check your permissions.');
    }
  };

  const sendAudioChunk = (audioData) => {
    if (websocket && encryptionEnabled) {
      const reader = new FileReader();
      reader.onload = () => {
        const arrayBuffer = reader.result;
        const encryptedData = encryptAudioData(arrayBuffer);
        websocket.send(JSON.stringify({
          type: 'audio_chunk',
          data: encryptedData,
          timestamp: Date.now(),
          sessionId: sessionData.id
        }));
      };
      reader.readAsArrayBuffer(audioData);
    }
  };

  const encryptAudioData = (data) => {
    // Simplified encryption - in production, use proper end-to-end encryption
    const encrypted = btoa(String.fromCharCode(...new Uint8Array(data)));
    return encrypted;
  };

  const loadSessionData = async (sessionId) => {
    // Mock loading session data with enhanced structure
    setSessionData(prev => ({
      ...prev,
      id: sessionId,
      conversationHistory: [
        {
          id: 1,
          speaker: 'user',
          content: 'Hello, I have been experiencing some chest discomfort lately.',
          timestamp: new Date(Date.now() - 300000),
          confidence: 96,
          speakerDiarization: { speaker: 'user', confidence: 0.98 }
        },
        {
          id: 2,
          speaker: 'Dr. Sarah Chen',
          content: 'I understand your concern about chest discomfort. Can you describe when you typically experience this discomfort?',
          timestamp: new Date(Date.now() - 240000),
          confidence: 98,
          speakerDiarization: { speaker: 'agent_1', confidence: 0.95 }
        }
      ],
      audioSessions: [
        {
          id: 'audio_1',
          title: 'Chest Discomfort Discussion',
          duration: 120,
          date: new Date().toLocaleDateString(),
          hasTranscript: true,
          quality: 'high'
        }
      ]
    }));
    setIsSessionActive(true);
    setSessionProgress(35);
    setupWebSocketConnection();
  };

  const handleStartConsultation = async () => {
    if (paymentRequired && !hasActiveSubscription()) {
      setShowPaymentModal(true);
      return;
    }

    if (!consentGiven) {
      alert('Please provide consent for audio processing before starting the consultation.');
      return;
    }

    // Initialize audio systems
    await initializeAudioCapture();

    // Start new consultation session
    const newSessionId = `session_${Date.now()}`;
    setSessionData(prev => ({
      ...prev,
      id: newSessionId,
      startTime: new Date(),
      activeAgents: [activeAgents[0]]
    }));

    setIsSessionActive(true);
    setCurrentPhase('listening');
    setSelectedAgent(activeAgents[0]);
    setupWebSocketConnection();
  };

  const handlePaymentSuccess = () => {
    setShowPaymentModal(false);
    setPaymentRequired(false);
    handleStartConsultation();
  };

  const handleVoiceActivation = async () => {
    if (currentPhase === 'ready' || currentPhase === 'listening') {
      if (!isRecording) {
        if (!mediaRecorder) {
          await initializeAudioCapture();
        }
        
        setIsRecording(true);
        setCurrentPhase('listening');
        audioChunksRef.current = [];
        
        if (mediaRecorder && mediaRecorder.state === 'inactive') {
          mediaRecorder.start(100); // Send data every 100ms
        }
      } else {
        setIsRecording(false);
        setCurrentPhase('processing');
        
        if (mediaRecorder && mediaRecorder.state === 'recording') {
          mediaRecorder.stop();
        }
        
        // Store audio for async messaging if enabled
        if (audioChunksRef.current.length > 0) {
          const audioBlob = new Blob(audioChunksRef.current, { type: 'audio/webm' });
          await storeSessionAudio(audioBlob);
        }
        
        // Simulate processing and response
        setTimeout(() => {
          setCurrentPhase('collaborating');
          handleAgentCollaboration({
            activeAgents: ['agent_1', 'agent_2'],
            collaborationType: 'symptom_analysis'
          });
          
          setTimeout(() => {
            setCurrentPhase('responding');
            setIsPlayingResponse(true);
            addTranscriptMessage('user', 'Can you help me understand my symptoms?', 96);
            
            setTimeout(() => {
              addTranscriptMessage('agent', 'Of course! Based on our collaborative analysis, I\'d be happy to help you understand your symptoms. Can you describe what you\'re experiencing in more detail?', 98, 'Dr. Sarah Chen');
              setCurrentPhase('listening');
              setIsPlayingResponse(false);
              setSessionProgress(prev => Math.min(prev + 10, 100));
            }, 3000);
          }, 2000);
        }, 2000);
      }
    }
  };

  const storeSessionAudio = async (audioBlob) => {
    try {
      // Get session token for encryption
      const sessionToken = generateSecureToken();

      const result = await audioStorageService.storeAudioMessage(audioBlob, {
        sessionId: sessionData.id,
        userId: user.id,
        speakerId: user.id,
        speakerName: user.full_name || 'You',
        messageType: 'session_audio',
        duration: audioChunksRef.current.length * 0.1, // Approximate duration
        quality: audioQuality,
        transcription: null,
        confidence: 0.95,
        sessionToken: sessionToken // Required for AES-256 encryption
      });

      if (result.success) {
        console.log('🔒 Audio stored securely with encryption:', result);

        // Add to session audio list with encryption metadata
        const audioSession = {
          id: result.messageId,
          title: `Session Audio - ${new Date().toLocaleTimeString()}`,
          duration: audioChunksRef.current.length * 0.1,
          date: new Date().toISOString(),
          hasTranscript: false,
          quality: audioQuality,
          encrypted: result.encrypted,
          audioBlob, // Keep original for immediate playback
          sessionToken: sessionToken // Store for later decryption
        };

        setSessionData(prev => ({
          ...prev,
          audioSessions: [...prev.audioSessions, audioSession]
        }));
      }
    } catch (error) {
      console.error('Failed to store session audio:', error);
    }
  };

  const handleAgentCollaboration = (data) => {
    setCurrentPhase('collaborating');
    
    // Update active agents based on collaboration
    const collaboratingAgents = activeAgents.filter(agent => 
      data.activeAgents?.includes(agent.id)
    );
    
    setSessionData(prev => ({
      ...prev,
      activeAgents: collaboratingAgents
    }));

    // Simulate inter-agent dialogue
    setTimeout(() => {
      addTranscriptMessage('system', `Dr. Sarah Chen is collaborating with Dr. Michael Rodriguez to analyze your symptoms...`, 95, 'System');
    }, 1000);
  };

  const handleAudioResponse = (data) => {
    setBufferingStatus(0);
    
    // Simulate buffering
    const bufferInterval = setInterval(() => {
      setBufferingStatus(prev => {
        if (prev >= 100) {
          clearInterval(bufferInterval);
          setIsPlayingResponse(true);
          return 100;
        }
        return prev + 10;
      });
    }, 100);

    // Create audio session entry
    const audioSession = {
      id: `audio_${Date.now()}`,
      title: `Response - ${new Date().toLocaleTimeString()}`,
      duration: data.duration || 0,
      date: new Date().toLocaleDateString(),
      hasTranscript: true,
      quality: audioQuality,
      audioBlob: data.audioBlob
    };

    setSessionData(prev => ({
      ...prev,
      audioSessions: [...prev.audioSessions, audioSession]
    }));
  };

  const addTranscriptMessage = (speaker, content, confidence = 95, speakerName = null) => {
    const newMessage = {
      id: Date.now(),
      speaker: speaker === 'agent' ? speakerName || selectedAgent?.name || 'AI Agent' : speaker === 'user' ? 'You' : speaker,
      content,
      timestamp: new Date(),
      confidence: confidence / 100,
      speakerDiarization: {
        speaker: speaker === 'user' ? 'user' : selectedAgent?.id || 'agent_1',
        confidence: confidence / 100
      }
    };
    
    setSessionData(prev => ({
      ...prev,
      transcriptMessages: [...prev.transcriptMessages, newMessage],
      conversationHistory: [...prev.conversationHistory, newMessage]
    }));
  };

  const updateSessionProgress = (progress) => {
    setSessionProgress(progress);
  };

  const handleEmergencyStop = () => {
    // Emergency cleanup
    cleanup();
    setIsSessionActive(false);
    setIsRecording(false);
    setCurrentPhase('ready');
    setSessionProgress(0);
    
    // Send emergency signal via WebSocket
    if (websocket && websocket.readyState === WebSocket.OPEN) {
      websocket.send(JSON.stringify({
        type: 'emergency_stop',
        sessionId: sessionData.id,
        timestamp: Date.now()
      }));
    }
  };

  const handleEndSession = async () => {
    // Proper session cleanup with data retention
    await saveSessionData();
    cleanup();
    setIsSessionActive(false);
    setCurrentPhase('ready');
    navigate('/session-dashboard-history');
  };

  const saveSessionData = async () => {
    if (!sessionData.id) return;

    try {
      // Save session with HIPAA compliance
      const sessionSummary = {
        id: sessionData.id,
        duration: sessionData.duration,
        transcriptCount: sessionData.transcriptMessages.length,
        audioSessionsCount: sessionData.audioSessions.length,
        retentionUntil: new Date(Date.now() + (dataRetentionDays * 24 * 60 * 60 * 1000)),
        encrypted: encryptionEnabled,
        emergencyStop: emergencyStopTriggered ? {
          triggered: true,
          reason: emergencyStopReason,
          timestamp: emergencyStopTime
        } : null
      };

      console.log('Saving session:', sessionSummary);
      // In production: await consultationService.saveSession(sessionSummary);
    } catch (error) {
      console.error('Failed to save session:', error);
    }
  };

  /**
   * EMERGENCY STOP MECHANISM
   * Immediately terminates consultation and triggers emergency protocols
   */
  const handleEmergencyStop = async (reason = 'user_initiated') => {
    try {
      console.log('🚨 EMERGENCY STOP TRIGGERED:', reason);

      // Set emergency stop flag immediately
      emergencyStopRef.current = true;
      setEmergencyStopTriggered(true);
      setEmergencyStopReason(reason);
      setEmergencyStopTime(new Date().toISOString());

      // Immediately stop all audio recording and processing
      if (mediaRecorder && mediaRecorder.state === 'recording') {
        mediaRecorder.stop();
      }

      if (mediaStream) {
        mediaStream.getTracks().forEach(track => track.stop());
        setMediaStream(null);
      }

      // Stop any ongoing audio playback
      setIsPlayingResponse(false);
      setIsRecording(false);

      // Close WebSocket connection
      if (websocket) {
        websocket.close();
        setWebsocket(null);
      }

      // Save current session state immediately
      await saveSessionData();

      // Log emergency stop event for audit trail
      await logEmergencyStopEvent(reason);

      // Trigger emergency protocols if needed
      if (reason === 'medical_emergency' || reason === 'critical_situation') {
        await triggerEmergencyProtocols();
      }

      // Show emergency dialog
      setShowEmergencyDialog(true);

      console.log('✅ Emergency stop completed successfully');

    } catch (error) {
      console.error('❌ Emergency stop failed:', error);
      // Even if logging fails, ensure the session is stopped
      setIsRecording(false);
      setIsPlayingResponse(false);
    }
  };

  /**
   * Log emergency stop event for audit trail
   */
  const logEmergencyStopEvent = async (reason) => {
    try {
      // Import audit logger
      const { default: auditLogger } = await import('../../utils/auditLogger');

      await auditLogger.logEmergencyAccess(
        user.id,
        user.id,
        `Emergency stop triggered: ${reason}`,
        {
          session_id: sessionData.id,
          stop_reason: reason,
          timestamp: new Date().toISOString(),
          user_role: user.user_metadata?.role,
          consultation_phase: currentPhase,
          recording_active: isRecording,
          response_time: '< 2 seconds'
        }
      );
    } catch (error) {
      console.error('Failed to log emergency stop event:', error);
    }
  };

  /**
   * Trigger emergency protocols for critical situations
   */
  const triggerEmergencyProtocols = async () => {
    try {
      // Check if user has emergency contact information
      const emergencyContacts = user.user_metadata?.emergency_contacts;

      if (emergencyContacts && emergencyContacts.length > 0) {
        console.log('🚨 Triggering emergency protocols...');

        // In production, this would:
        // 1. Send notifications to emergency contacts
        // 2. Alert healthcare providers
        // 3. Potentially contact emergency services
        // 4. Escalate to on-call medical staff

        // For now, log the action
        console.log('Emergency protocols would be triggered for:', emergencyContacts);
      }
    } catch (error) {
      console.error('Failed to trigger emergency protocols:', error);
    }
  };

  /**
   * Handle emergency dialog actions
   */
  const handleEmergencyDialogAction = (action) => {
    switch (action) {
      case 'resume':
        // Only allow resume for non-critical emergencies
        if (emergencyStopReason !== 'medical_emergency') {
          setEmergencyStopTriggered(false);
          setShowEmergencyDialog(false);
          emergencyStopRef.current = false;
        }
        break;
      case 'end_session':
        setShowEmergencyDialog(false);
        handleEndSession();
        break;
      case 'contact_emergency':
        // Trigger emergency contact protocols
        triggerEmergencyProtocols();
        break;
      default:
        setShowEmergencyDialog(false);
    }
  };

  const cleanup = () => {
    // Stop recording
    if (mediaRecorder && mediaRecorder.state === 'recording') {
      mediaRecorder.stop();
    }

    // Close media stream
    if (mediaStream) {
      mediaStream.getTracks().forEach(track => track.stop());
      setMediaStream(null);
    }

    // Close WebSocket
    if (websocket) {
      websocket.close();
      setWebsocket(null);
    }

    // Cancel animation frames
    if (animationFrameRef.current) {
      cancelAnimationFrame(animationFrameRef.current);
    }

    // Clear audio chunks
    audioChunksRef.current = [];

    setConnectionStatus('disconnected');
  };

  const formatDuration = (seconds) => {
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}:${remainingSeconds.toString().padStart(2, '0')}`;
  };

  if (!user) {
    return null;
  }

  if (isLoading) {
    return (
      <div className="min-h-screen bg-background">
        <Header />
        <div className="flex items-center justify-center min-h-[80vh]">
          <div className="text-center">
            <div className="w-12 h-12 bg-primary-50 rounded-full flex items-center justify-center mb-4 mx-auto animate-breathe">
              <Icon name="Activity" size={24} color="var(--color-primary)" />
            </div>
            <p className="text-text-secondary">Initializing enhanced consultation...</p>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-background">
      <Header />
      
      {/* Session Status Indicator */}
      {isSessionActive && (
        <ConsultationStatusIndicator
          isActive={isSessionActive}
          activeAgents={sessionData.activeAgents}
          sessionProgress={sessionProgress}
          consultationPhase={currentPhase}
          connectionStatus={connectionStatus}
          onEmergencyStop={handleEmergencyStop}
        />
      )}

      {/* Async Message Notifications */}
      {asyncMessageNotifications.length > 0 && !showAsyncMessages && (
        <div className="fixed top-20 left-4 z-50 space-y-2">
          {asyncMessageNotifications.slice(0, 3).map((notification, index) => (
            <div
              key={notification.messageId}
              className="bg-primary-500 text-white rounded-lg p-3 shadow-lg cursor-pointer hover:bg-primary-600 transition-colors max-w-sm"
              onClick={() => handleOpenAsyncMessage(notification.messageId)}
            >
              <div className="flex items-center space-x-2">
                <Icon name="MessageSquare" size={16} />
                <span className="text-sm font-medium">New Voice Message</span>
              </div>
              <p className="text-xs mt-1 opacity-90">
                {formatDuration(notification.duration)} voice message received
              </p>
            </div>
          ))}
          {asyncMessageNotifications.length > 3 && (
            <div className="text-center">
              <Button
                onClick={() => setShowAsyncMessages(true)}
                size="sm"
                variant="outline"
                className="bg-white"
              >
                +{asyncMessageNotifications.length - 3} more
              </Button>
            </div>
          )}
        </div>
      )}

      <main className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-20 pb-12">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Consultation Area */}
          <div className="lg:col-span-3">
            {/* Session Header with Enhanced Controls */}
            <div className="bg-surface rounded-xl shadow-minimal border border-border p-6 mb-6">
              <div className="flex items-center justify-between mb-4">
                <div>
                  <h1 className="text-2xl font-bold text-text-primary font-heading">
                    {isSessionActive ? 'Enhanced Voice Consultation' : 'Enhanced AI Voice Consultation'}
                  </h1>
                  <p className="text-text-secondary mt-1">
                    {isSessionActive 
                      ? `Session Duration: ${formatDuration(sessionData.duration)} | Connection: ${connectionStatus}`
                      : eligibilityStatus?.canStart 
                      ? 'Ready to start your enhanced AI-powered health consultation with real-time audio processing and async messaging' :'Payment required to start consultation'
                    }
                  </p>
                </div>
                
                <div className="flex items-center space-x-3">
                  {/* Async Message Toggle */}
                  <Button
                    variant="outline"
                    onClick={() => setShowAsyncMessages(!showAsyncMessages)}
                    iconName="MessageSquare"
                    iconPosition="left"
                    size="sm"
                    className={`relative ${asyncMessageNotifications.length > 0 ? 'border-primary-500 text-primary-600' : ''}`}
                  >
                    Messages
                    {asyncMessageNotifications.length > 0 && (
                      <span className="absolute -top-2 -right-2 bg-error-500 text-white text-xs rounded-full w-5 h-5 flex items-center justify-center">
                        {asyncMessageNotifications.length > 9 ? '9+' : asyncMessageNotifications.length}
                      </span>
                    )}
                  </Button>
                  
                  {isSessionActive && (
                    <>
                      <SessionProgressIndicator progress={sessionProgress} />
                      <Button
                        variant="outline"
                        onClick={() => setShowSecurityPanel(!showSecurityPanel)}
                        iconName="Shield"
                        iconPosition="left"
                        size="sm"
                      >
                        Security
                      </Button>
                      <Button
                        variant="outline"
                        onClick={handleEndSession}
                        iconName="Square"
                        iconPosition="left"
                        size="sm"
                      >
                        End Session
                      </Button>
                    </>
                  )}
                </div>
              </div>

              {/* Enhanced Audio System Status */}
              {isSessionActive && (
                <div className="bg-secondary-50 rounded-lg p-4 mb-4">
                  <div className="grid grid-cols-2 md:grid-cols-5 gap-4 text-sm">
                    <div>
                      <span className="text-text-secondary">Audio Quality:</span>
                      <span className="ml-2 font-medium text-text-primary">{audioQuality}</span>
                    </div>
                    <div>
                      <span className="text-text-secondary">Noise Reduction:</span>
                      <span className="ml-2 font-medium text-text-primary">{noiseReduction ? 'ON' : 'OFF'}</span>
                    </div>
                    <div>
                      <span className="text-text-secondary">Connection:</span>
                      <span className={`ml-2 font-medium ${
                        connectionQuality === 'excellent' ? 'text-success-600' :
                        connectionQuality === 'good' ? 'text-warning-600' : 'text-error-600'
                      }`}>
                        {connectionQuality}
                      </span>
                    </div>
                    <div>
                      <span className="text-text-secondary">Encryption:</span>
                      <span className="ml-2 font-medium text-success-600">{encryptionEnabled ? 'Enabled' : 'Disabled'}</span>
                    </div>
                    <div>
                      <span className="text-text-secondary">Async Storage:</span>
                      <span className={`ml-2 font-medium ${
                        storageStats?.usagePercentage > 80 ? 'text-warning-600' : 'text-success-600'
                      }`}>
                        {storageStats ? `${Math.round(storageStats.usagePercentage)}%` : 'Ready'}
                      </span>
                    </div>
                  </div>
                </div>
              )}

              {/* Payment Warning */}
              {paymentRequired && !hasActiveSubscription() && (
                <div className="bg-orange-50 border border-orange-200 rounded-lg p-4 mb-4">
                  <div className="flex items-start">
                    <Icon name="AlertCircle" size={20} className="text-orange-600 mt-0.5 mr-3" />
                    <div>
                      <h3 className="text-sm font-medium text-orange-800 mb-1">
                        Payment Required
                      </h3>
                      <p className="text-sm text-orange-700 mb-2">
                        You need to pay for this consultation or subscribe to a plan to continue.
                      </p>
                      <div className="flex space-x-3">
                        <Button
                          size="sm"
                          onClick={() => setShowPaymentModal(true)}
                        >
                          Pay ₦{consultationFee.toLocaleString()}
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => navigate('/payment-plans')}
                        >
                          View Plans
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Consent Required */}
              {!consentGiven && (
                <div className="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-4">
                  <div className="flex items-start">
                    <Icon name="Info" size={20} className="text-blue-600 mt-0.5 mr-3" />
                    <div className="flex-1">
                      <h3 className="text-sm font-medium text-blue-800 mb-1">
                        Audio Processing Consent Required
                      </h3>
                      <p className="text-sm text-blue-700 mb-3">
                        This enhanced consultation requires your consent for real-time audio processing, secure transmission, temporary storage for session analysis, and async messaging capabilities.
                      </p>
                      <div className="flex items-center space-x-3">
                        <Button
                          size="sm"
                          onClick={() => setConsentGiven(true)}
                        >
                          I Consent
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => setShowSecurityPanel(true)}
                        >
                          View Privacy Details
                        </Button>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {/* Subscription Status */}
              {hasActiveSubscription() && (
                <div className="bg-green-50 border border-green-200 rounded-lg p-4 mb-4">
                  <div className="flex items-center">
                    <Icon name="CheckCircle" size={16} className="text-green-600 mr-2" />
                    <span className="text-sm text-green-800">
                      {getRemainingCredits() === Infinity 
                        ? 'Unlimited enhanced consultations with async messaging available' 
                        : `${getRemainingCredits()} enhanced consultations remaining`
                      }
                    </span>
                  </div>
                </div>
              )}

              {/* Agent Status */}
              {isSessionActive && selectedAgent && (
                <div className="flex items-center justify-between">
                  <ActiveAgentIndicator 
                    agent={selectedAgent}
                    isActive={currentPhase !== 'ready'}
                    processingPhase={currentPhase}
                    className="mb-0"
                  />
                  
                  <MultiAgentStatus 
                    agents={activeAgents}
                    currentPhase={currentPhase}
                    collaborationActive={currentPhase === 'collaborating'}
                    onAgentToggle={(agentId) => {
                      console.log('Toggle agent:', agentId);
                    }}
                  />
                </div>
              )}
            </div>

            {/* Enhanced Voice Activation Area with Audio Visualization */}
            <div className="bg-surface rounded-xl shadow-minimal border border-border p-8 mb-6">
              <div className="text-center">
                {/* Advanced Audio Visualization */}
                {isSessionActive && (
                  <AudioVisualization
                    frequencyData={frequencyData}
                    audioLevel={audioLevel}
                    isRecording={isRecording}
                    currentPhase={currentPhase}
                    className="mb-6"
                  />
                )}

                <VoiceActivationButton
                  isRecording={isRecording}
                  audioLevel={audioLevel}
                  sessionPhase={currentPhase}
                  onActivate={handleVoiceActivation}
                  disabled={!isSessionActive && (!eligibilityStatus?.canStart || paymentRequired || !consentGiven)}
                  showAdvancedVisuals={true}
                />

                {!isSessionActive && (
                  <div className="mt-6">
                    <Button
                      onClick={handleStartConsultation}
                      size="lg"
                      disabled={!eligibilityStatus?.canStart || paymentRequired || !consentGiven}
                      iconName="Play"
                      iconPosition="left"
                    >
                      {paymentRequired ? 'Payment Required' : !consentGiven ? 'Consent Required' : 'Start Enhanced Consultation'}
                    </Button>
                  </div>
                )}

                {/* Enhanced Phase Indicator */}
                <div className="mt-4">
                  <p className="text-sm text-text-secondary">
                    {currentPhase === 'ready' && 'Ready to listen with advanced audio processing and async messaging'}
                    {currentPhase === 'listening' && 'Listening with noise suppression and echo cancellation...'}
                    {currentPhase === 'processing' && 'Processing your input with AI analysis...'}
                    {currentPhase === 'collaborating' && 'Multi-agent collaboration in progress...'}
                    {currentPhase === 'responding' && 'AI agents are responding with enhanced audio...'}
                  </p>
                  {isSessionActive && bufferingStatus > 0 && bufferingStatus < 100 && (
                    <div className="mt-2">
                      <div className="w-full bg-secondary-200 rounded-full h-1">
                        <div 
                          className="bg-primary-500 h-1 rounded-full transition-all duration-300"
                          style={{ width: `${bufferingStatus}%` }}
                        ></div>
                      </div>
                      <p className="text-xs text-text-muted mt-1">Buffering audio response...</p>
                    </div>
                  )}
                </div>
              </div>
            </div>

            {/* WebSocket Connection Manager */}
            {isSessionActive && (
              <WebSocketManager
                websocket={websocket}
                connectionStatus={connectionStatus}
                connectionQuality={connectionQuality}
                reconnectAttempts={reconnectAttempts}
                onManualReconnect={setupWebSocketConnection}
                className="mb-6"
              />
            )}

            {/* Enhanced Real-time Transcription */}
            {isSessionActive && (
              <RealTimeTranscription
                transcriptionData={sessionData.transcriptMessages}
                isListening={isRecording}
                isProcessing={currentPhase === 'processing'}
                speakerDiarization={sessionData.speakerDiarization}
                showConfidenceScores={true}
                onClearTranscription={() => {
                  setSessionData(prev => ({
                    ...prev,
                    transcriptMessages: []
                  }));
                }}
                className="mb-6"
              />
            )}

            {/* Enhanced Audio Playback Controls */}
            {isSessionActive && sessionData.audioSessions.length > 0 && (
              <AudioPlaybackControls
                audioSessions={sessionData.audioSessions}
                currentSessionId={sessionData.id}
                isPlaying={isPlayingResponse}
                onPlaySession={(sessionId) => {
                  setIsPlayingResponse(true);
                  console.log('Playing session:', sessionId);
                }}
                onPauseSession={(sessionId) => {
                  setIsPlayingResponse(false);
                  console.log('Pausing session:', sessionId);
                }}
                onSeekTo={(sessionId, time) => {
                  console.log('Seeking to:', time);
                }}
                onSpeedChange={(sessionId, speed) => {
                  console.log('Speed changed to:', speed);
                }}
                showWaveform={true}
                enableDownload={true}
                className="mb-6"
              />
            )}

            {/* Noise Suppression Controls */}
            {isSessionActive && (
              <NoiseSuppressionControls
                noiseReduction={noiseReduction}
                echoCancellation={echoCancellation}
                autoGainControl={autoGainControl}
                audioQuality={audioQuality}
                onNoiseReductionChange={setNoiseReduction}
                onEchoCancellationChange={setEchoCancellation}
                onAutoGainControlChange={setAutoGainControl}
                onAudioQualityChange={setAudioQuality}
                className="mb-6"
              />
            )}

            {/* Quick Actions */}
            {isSessionActive && (
              <QuickActionControls
                onRequestSpecialist={() => console.log('Request specialist')}
                onTakeNotes={() => console.log('Take notes')}
                onScheduleFollowup={() => console.log('Schedule followup')}
                onEmergencyAlert={handleEmergencyStop}
                onToggleRecording={handleVoiceActivation}
                isRecording={isRecording}
              />
            )}
          </div>

          {/* Enhanced Sidebar */}
          <div className="lg:col-span-1 space-y-6">
            {/* Async Message Panel */}
            <AsyncMessagePanel
              sessionId={sessionData.id}
              recipientId={activeAgents[0]?.id}
              isVisible={showAsyncMessages}
              onToggleVisibility={() => setShowAsyncMessages(!showAsyncMessages)}
            />

            {/* Conversation History */}
            <ConversationHistorySidebar
              conversationHistory={sessionData.conversationHistory}
              isVisible={showConversationHistory}
              speakerDiarization={sessionData.speakerDiarization}
              onToggleVisibility={() => setShowConversationHistory(!showConversationHistory)}
              onClearHistory={() => {
                setSessionData(prev => ({
                  ...prev,
                  conversationHistory: [],
                  transcriptMessages: []
                }));
              }}
            />

            {/* Security Controls Panel */}
            {(isSessionActive || showSecurityPanel) && (
              <SecurityControls
                encryptionEnabled={encryptionEnabled}
                localProcessing={localProcessing}
                dataRetentionDays={dataRetentionDays}
                sessionToken={sessionToken}
                onEncryptionToggle={setEncryptionEnabled}
                onLocalProcessingToggle={setLocalProcessing}
                onDataRetentionChange={setDataRetentionDays}
                onSessionCleanup={cleanup}
                isVisible={showSecurityPanel}
                onToggleVisibility={() => setShowSecurityPanel(!showSecurityPanel)}
              />
            )}
          </div>
        </div>
      </main>

      {/* Payment Modal */}
      <PaymentModal
        isOpen={showPaymentModal}
        onClose={() => setShowPaymentModal(false)}
        onSuccess={handlePaymentSuccess}
        paymentType="consultation"
        consultationId={sessionData.id}
        amount={consultationFee}
        currency="NGN"
        planDetails={{
          name: 'Enhanced Single Consultation',
          description: 'Pay for individual enhanced consultation session with advanced audio processing and async messaging',
          features: [
            'Advanced AI-powered consultation', 
            'Real-time audio processing', 
            'Multi-agent collaboration',
            'Secure session recording',
            'Advanced transcription with speaker diarization',
            'End-to-end encryption',
            'Async voice messaging',
            'Audio storage and playback',
            'Push notifications for messages'
          ]
        }}
      />
    </div>
  );
};

export default EnhancedVoiceConsultationInterface;